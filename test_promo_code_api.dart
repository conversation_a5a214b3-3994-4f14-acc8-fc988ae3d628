import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Comprehensive test script to verify promo code API functionality
/// This script tests different payload formats and authentication methods
class PromoCodeApiTester {
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  static const String verifyEndpoint = '$baseUrl/promocodes/verify';
  static const String promoCodesEndpoint = '$baseUrl/user/promocodes?promo_type=recharge';
  
  // Test promo codes from the documentation
  static const List<String> testPromoCodes = [
    'WELCOME10',
    'SAVE15', 
    'BONUS20',
    'FIRST25',
    'SERVER15',
    'TEST123',  // Invalid code for testing
  ];

  /// Main test runner
  static Future<void> runAllTests() async {
    print('🔔 ===== PROMO CODE API COMPREHENSIVE TESTING =====');
    print('🔔 Testing API: $verifyEndpoint');
    print('🔔 ================================================\n');

    // Test 1: Find correct API endpoints
    await testDifferentEndpoints();

    // Test 2: Check API endpoint accessibility
    await testApiEndpointAccessibility();

    // Test 3: Test different payload formats
    await testDifferentPayloadFormats();

    // Test 4: Test authentication methods
    await testAuthenticationMethods();

    // Test 5: Test available promo codes endpoint
    await testAvailablePromoCodesEndpoint();

    // Test 6: Test with sample promo codes
    await testSamplePromoCodes();

    print('\n🔔 ===== ALL TESTS COMPLETED =====');
  }

  /// Test 1: Try different endpoint variations to find the correct one
  static Future<void> testDifferentEndpoints() async {
    print('🧪 TEST 1: Finding Correct API Endpoints');
    print('=' * 50);

    final endpointVariations = [
      '$baseUrl/promocodes/verify',
      '$baseUrl/promocode/verify',
      '$baseUrl/promo/verify',
      '$baseUrl/user/promocodes/verify',
      '$baseUrl/user/promocode/verify',
      '$baseUrl/user/promo/verify',
      '$baseUrl/verify-promocode',
      '$baseUrl/verify-promo',
    ];

    for (final endpoint in endpointVariations) {
      print('📤 Testing endpoint: $endpoint');

      try {
        final response = await http.post(
          Uri.parse(endpoint),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'code': 'TEST'}),
        ).timeout(Duration(seconds: 5));

        print('📊 Status: ${response.statusCode}');
        if (response.statusCode != 404) {
          print('✅ FOUND WORKING ENDPOINT: $endpoint');
          print('📥 Response: ${response.body.length > 100 ? response.body.substring(0, 100) + "..." : response.body}');
        }

      } catch (e) {
        print('❌ Error: $e');
      }
      print('');
    }
  }

  /// Test 2: Check if API endpoint is accessible
  static Future<void> testApiEndpointAccessibility() async {
    print('🧪 TEST 1: API Endpoint Accessibility');
    print('=' * 50);
    
    try {
      final response = await http.post(
        Uri.parse(verifyEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'test': 'connectivity'}),
      ).timeout(Duration(seconds: 10));
      
      print('✅ Endpoint accessible');
      print('📊 Status Code: ${response.statusCode}');
      print('📥 Response: ${response.body}');
      
    } catch (e) {
      print('❌ Endpoint accessibility error: $e');
    }
    print('');
  }

  /// Test 2: Test different payload formats to find the correct one
  static Future<void> testDifferentPayloadFormats() async {
    print('🧪 TEST 2: Different Payload Formats');
    print('=' * 50);
    
    final testCode = 'SAVE15';
    final payloadFormats = [
      {'code': testCode},                    // Format 1
      {'promo': testCode},                   // Format 2  
      {'promo_code': testCode},              // Format 3
      {'promocode': testCode},               // Format 4
      {'promo_code': testCode, 'amount': 1000}, // Format 5 with amount
    ];
    
    for (int i = 0; i < payloadFormats.length; i++) {
      final payload = payloadFormats[i];
      print('📤 Testing Format ${i + 1}: ${jsonEncode(payload)}');
      
      try {
        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 10));
        
        print('📊 Status: ${response.statusCode}');
        print('📥 Response: ${response.body}');
        
        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          if (responseData['success'] == true) {
            print('✅ SUCCESS: Format ${i + 1} works!');
          }
        }
        
      } catch (e) {
        print('❌ Error with format ${i + 1}: $e');
      }
      print('');
    }
  }

  /// Test 3: Test different authentication methods
  static Future<void> testAuthenticationMethods() async {
    print('🧪 TEST 3: Authentication Methods');
    print('=' * 50);
    
    final payload = {'code': 'SAVE15'};
    
    // Test without auth
    print('📤 Testing without authentication...');
    await makeTestRequest(payload, null);
    
    // Test with dummy token
    print('📤 Testing with dummy Bearer token...');
    await makeTestRequest(payload, 'Bearer dummy_token_123');
    
    // Test with different auth formats
    final authFormats = [
      'Token dummy_token_123',
      'Bearer test_token',
      'API-Key test_key',
    ];
    
    for (final auth in authFormats) {
      print('📤 Testing with auth: $auth');
      await makeTestRequest(payload, auth);
    }
    print('');
  }

  /// Helper method to make test requests
  static Future<void> makeTestRequest(Map<String, dynamic> payload, String? authHeader) async {
    try {
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
      
      if (authHeader != null) {
        headers['Authorization'] = authHeader;
      }
      
      final response = await http.post(
        Uri.parse(verifyEndpoint),
        headers: headers,
        body: jsonEncode(payload),
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body.length > 200 ? response.body.substring(0, 200) + "..." : response.body}');
      
    } catch (e) {
      print('❌ Request error: $e');
    }
    print('');
  }

  /// Test 4: Test available promo codes endpoint
  static Future<void> testAvailablePromoCodesEndpoint() async {
    print('🧪 TEST 4: Available Promo Codes Endpoint');
    print('=' * 50);
    
    try {
      final response = await http.get(
        Uri.parse(promoCodesEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(Duration(seconds: 10));
      
      print('📊 Status: ${response.statusCode}');
      print('📥 Response: ${response.body}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['data'] is List) {
          final promoCodes = responseData['data'] as List;
          print('✅ Found ${promoCodes.length} promo codes');
          
          // Print first few promo codes for analysis
          for (int i = 0; i < promoCodes.length && i < 3; i++) {
            print('🎫 Promo ${i + 1}: ${jsonEncode(promoCodes[i])}');
          }
        }
      }
      
    } catch (e) {
      print('❌ Error fetching promo codes: $e');
    }
    print('');
  }

  /// Test 5: Test with sample promo codes
  static Future<void> testSamplePromoCodes() async {
    print('🧪 TEST 5: Sample Promo Codes Testing');
    print('=' * 50);
    
    for (final code in testPromoCodes) {
      print('🎫 Testing promo code: $code');
      
      final payload = {'code': code};
      
      try {
        final response = await http.post(
          Uri.parse(verifyEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(payload),
        ).timeout(Duration(seconds: 10));
        
        print('📊 Status: ${response.statusCode}');
        final responseBody = response.body;
        print('📥 Response: ${responseBody.length > 150 ? responseBody.substring(0, 150) + "..." : responseBody}');
        
        if (response.statusCode == 200) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData['success'] == true) {
              print('✅ SUCCESS: $code is valid!');
              if (responseData['data'] != null) {
                print('💰 Credits: ${responseData['data']['credits']}');
                print('💵 Min Amount: ${responseData['data']['minimum_amount_applicable']}');
              }
            } else {
              print('❌ FAILED: ${responseData['message'] ?? 'Invalid code'}');
            }
          } catch (e) {
            print('❌ JSON Parse Error: $e');
          }
        }
        
      } catch (e) {
        print('❌ Request error for $code: $e');
      }
      print('');
    }
  }
}

/// Main function to run the tests
void main() async {
  await PromoCodeApiTester.runAllTests();
}
